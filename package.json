{"name": "ride-atlas", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "prisma generate && next build", "vercel-build": "prisma generate && prisma db push && next build", "postinstall": "prisma generate && prisma db push", "setup": "npm install && prisma db push", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest src/tests/unit", "test:integration": "jest src/tests/integration", "test:e2e": "jest src/tests/e2e", "test:ci": "jest --ci --coverage --watchAll=false", "db:push": "prisma db push", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "test:prisma": "npx tsx src/scripts/test-prisma.ts", "create:sentinel": "npx tsx src/scripts/create-sentinel.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@aws-sdk/client-s3": "^3.842.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.8.2", "@stripe/react-stripe-js": "^3.9.0", "@stripe/stripe-js": "^7.8.0", "@types/nodemailer": "^6.4.17", "@vercel/blob": "^1.1.1", "bcryptjs": "^3.0.2", "crypto": "^1.0.1", "fast-xml-parser": "^5.2.3", "leaflet": "^1.9.4", "lucide-react": "^0.511.0", "mapbox-gl": "^2.15.0", "next": "14.2.28", "next-auth": "^5.0.0-beta.28", "nodemailer": "^6.10.1", "react": "^18.2.0", "react-cookie-consent": "^9.0.0", "react-dom": "^18.2.0", "react-hook-form": "^7.62.0", "react-leaflet": "^4.2.1", "react-map-gl": "^7.1.6", "react-markdown": "^10.1.0", "stripe": "^18.4.0", "zod": "^3.25.76"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.11", "@types/leaflet": "^1.9.18", "@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.17", "@types/stripe": "^8.0.416", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-config-next": "14.2.28", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "prisma": "^6.8.2", "tailwindcss": "^3.3.6", "tsx": "^4.19.4", "typescript": "^5.3.3", "undici": "^7.10.0", "whatwg-fetch": "^3.6.20"}}