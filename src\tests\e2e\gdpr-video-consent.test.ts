/**
 * End-to-End GDPR Compliance Tests
 * 
 * Tests the complete user journey for GDPR-compliant video consent,
 * from first visit to video viewing with proper consent management.
 */

import { test, expect } from '@playwright/test';

test.describe('GDPR Video Consent - Complete User Journey', () => {
  
  test.beforeEach(async ({ context }) => {
    // Clear all cookies and localStorage to simulate fresh user
    await context.clearCookies();
    await context.clearPermissions();
  });

  test('New user sees cookie banner and must consent for videos', async ({ page }) => {
    // Mock a page with video content
    await page.goto('/trips/test-trip-with-video'); // Assuming such a route exists
    
    // Step 1: User sees cookie banner on first visit
    await expect(page.locator('text=Utilizzo dei Cookie')).toBeVisible();
    await expect(page.locator('button:has-text("Accetta tutti")')).toBeVisible();
    await expect(page.locator('button:has-text("Rifiuta opzionali")')).toBeVisible();
    await expect(page.locator('button:has-text("Personalizza")')).toBeVisible();
    
    // Step 2: User initially rejects optional cookies
    await page.click('button:has-text("Rifiuta opzionali")');
    
    // Cookie banner should disappear
    await expect(page.locator('text=Utilizzo dei Cookie')).not.toBeVisible();
    
    // Step 3: User encounters a video and sees consent request
    // Assuming the page has a video component
    const videoConsentBanner = page.locator('text=Consenso richiesto');
    if (await videoConsentBanner.isVisible()) {
      await expect(videoConsentBanner).toBeVisible();
      await expect(page.locator('text=Questo video YouTube richiede')).toBeVisible();
      await expect(page.locator('button:has-text("Accetta per tutti i video")')).toBeVisible();
      await expect(page.locator('button:has-text("Solo questo video")')).toBeVisible();
    }
  });

  test('User can manage cookie preferences and see changes reflected immediately', async ({ page }) => {
    await page.goto('/cookie-settings');
    
    // Step 1: Verify cookie settings page loads
    await expect(page.locator('text=Impostazioni Cookie')).toBeVisible();
    await expect(page.locator('text=Cookie Essenziali')).toBeVisible();
    await expect(page.locator('text=Video Esterni')).toBeVisible();
    
    // Step 2: Verify essential cookies cannot be disabled
    const essentialSection = page.locator('text=Cookie Essenziali').locator('..');
    await expect(essentialSection.locator('text=Sempre attivo')).toBeVisible();
    
    // Step 3: Enable video consent
    const videoToggle = page.locator('text=Video Esterni')
      .locator('..')
      .locator('input[type="checkbox"]');
    
    if (await videoToggle.isVisible()) {
      await videoToggle.check();
      await page.click('button:has-text("Salva modifiche")');
      
      // Should show success message
      await expect(page.locator('text=Impostazioni salvate con successo')).toBeVisible();
    }
    
    // Step 4: Verify changes persist on page reload
    await page.reload();
    const videoToggleAfterReload = page.locator('text=Video Esterni')
      .locator('..')
      .locator('input[type="checkbox"]');
    
    if (await videoToggleAfterReload.isVisible()) {
      await expect(videoToggleAfterReload).toBeChecked();
    }
  });

  test('Privacy policy contains comprehensive GDPR information', async ({ page }) => {
    await page.goto('/privacy-policy');
    
    // Step 1: Verify GDPR-required sections
    await expect(page.locator('text=Privacy Policy')).toBeVisible();
    await expect(page.locator('h2:has-text("Cookie")')).toBeVisible();
    await expect(page.locator('h2:has-text("Video YouTube Embedded")')).toBeVisible();
    
    // Step 2: Verify specific GDPR information for videos
    await expect(page.locator('text=consenso esplicito prima di essere caricati')).toBeVisible();
    await expect(page.locator('text=Indirizzo IP')).toBeVisible();
    await expect(page.locator('text=Cookie e identificatori')).toBeVisible();
    
    // Step 3: Verify links to YouTube privacy policy
    const youtubePrivacyLink = page.locator('a[href*="policies.google.com/privacy"]');
    await expect(youtubePrivacyLink).toBeVisible();
    await expect(youtubePrivacyLink).toHaveAttribute('target', '_blank');
    await expect(youtubePrivacyLink).toHaveAttribute('rel', 'noopener noreferrer');
    
    // Step 4: Verify consent revocation information
    await expect(page.locator('text=revocare il consenso')).toBeVisible();
  });

  test('Complete video consent flow - granular choices work correctly', async ({ page }) => {
    // This test assumes a test page with embedded video exists
    await page.goto('/test/video-consent-demo'); // Mock test page
    
    // Step 1: Initial state - no consent given
    await page.evaluate(() => {
      localStorage.removeItem('rideatlas-consents');
    });
    await page.reload();
    
    // Should see consent banner for video
    const consentBanner = page.locator('text=Consenso richiesto');
    if (await consentBanner.isVisible()) {
      // Step 2: Test "Only this video" option
      await page.click('button:has-text("Solo questo video")');
      
      // Video should load but no permanent consent saved
      const iframe = page.locator('iframe[src*="youtube.com"]');
      await expect(iframe).toBeVisible();
      
      // Reload page - should ask for consent again
      await page.reload();
      if (await page.locator('text=Consenso richiesto').isVisible()) {
        // Step 3: Test "Accept for all videos" option
        await page.click('button:has-text("Accetta per tutti i video")');
        
        // Video should load and consent should be saved
        await expect(page.locator('iframe[src*="youtube.com"]')).toBeVisible();
        
        // Reload page - should NOT ask for consent again
        await page.reload();
        await expect(page.locator('text=Consenso richiesto')).not.toBeVisible();
      }
    }
  });

  test('User can revoke video consent and it takes effect immediately', async ({ page }) => {
    // Step 1: Start with video consent granted
    await page.goto('/cookie-settings');
    
    const videoToggle = page.locator('text=Video Esterni')
      .locator('..')
      .locator('input[type="checkbox"]');
      
    if (await videoToggle.isVisible()) {
      await videoToggle.check();
      await page.click('button:has-text("Salva modifiche")');
    }
    
    // Step 2: Go to page with video - should load without consent request
    await page.goto('/test/video-consent-demo');
    
    if (await page.locator('iframe[src*="youtube.com"]').isVisible()) {
      // Video loads directly, good!
      
      // Step 3: Revoke consent
      await page.goto('/cookie-settings');
      
      const videoToggleRevoke = page.locator('text=Video Esterni')
        .locator('..')
        .locator('input[type="checkbox"]');
        
      if (await videoToggleRevoke.isVisible()) {
        await videoToggleRevoke.uncheck();
        await page.click('button:has-text("Salva modifiche")');
      }
      
      // Step 4: Return to video page - should now ask for consent
      await page.goto('/test/video-consent-demo');
      await expect(page.locator('text=Consenso richiesto')).toBeVisible();
      await expect(page.locator('iframe[src*="youtube.com"]')).not.toBeVisible();
    }
  });

  test('GDPR compliance - no external requests without consent', async ({ page }) => {
    // Monitor network requests
    const requests: string[] = [];
    page.on('request', request => {
      const url = request.url();
      if (url.includes('youtube.com') || url.includes('google.com')) {
        requests.push(url);
      }
    });
    
    // Step 1: Visit page with video, no consent given
    await page.evaluate(() => {
      localStorage.removeItem('rideatlas-consents');
    });
    
    await page.goto('/test/video-consent-demo');
    
    // Wait a moment for any potential requests
    await page.waitForTimeout(2000);
    
    // Step 2: Verify no YouTube/Google requests made
    const youtubeRequests = requests.filter(url => 
      url.includes('youtube.com') && !url.includes('img.youtube.com')
    );
    expect(youtubeRequests).toHaveLength(0);
    
    // Step 3: Give consent and verify requests are now allowed
    const consentBanner = page.locator('text=Consenso richiesto');
    if (await consentBanner.isVisible()) {
      await page.click('button:has-text("Accetta per tutti i video")');
      
      // Wait for video to load
      await page.waitForSelector('iframe[src*="youtube.com"]', { timeout: 5000 });
      
      // Now YouTube requests should be present
      const youtubeRequestsAfterConsent = requests.filter(url => 
        url.includes('youtube.com/embed')
      );
      expect(youtubeRequestsAfterConsent.length).toBeGreaterThan(0);
    }
  });

  test('Cookie banner accessibility and keyboard navigation', async ({ page }) => {
    await page.goto('/');
    
    // Step 1: Verify banner is focusable and has proper ARIA labels
    const banner = page.locator('text=Utilizzo dei Cookie').locator('..');
    if (await banner.isVisible()) {
      // Tab through banner elements
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      
      // Verify buttons are focusable
      const acceptButton = page.locator('button:has-text("Accetta tutti")');
      const rejectButton = page.locator('button:has-text("Rifiuta opzionali")');
      const customizeButton = page.locator('button:has-text("Personalizza")');
      
      await expect(acceptButton).toBeVisible();
      await expect(rejectButton).toBeVisible();
      await expect(customizeButton).toBeVisible();
      
      // Test keyboard activation
      await acceptButton.focus();
      await page.keyboard.press('Enter');
      
      // Banner should close
      await expect(banner).not.toBeVisible();
    }
  });

  test('Data retention - consent choices persist correctly', async ({ page, context }) => {
    // Step 1: Make consent choice
    await page.goto('/cookie-settings');
    
    const videoToggle = page.locator('text=Video Esterni')
      .locator('..')
      .locator('input[type="checkbox"]');
      
    if (await videoToggle.isVisible()) {
      await videoToggle.check();
      await page.click('button:has-text("Salva modifiche")');
    }
    
    // Step 2: Verify localStorage persistence
    const consentData = await page.evaluate(() => {
      return localStorage.getItem('rideatlas-consents');
    });
    
    expect(consentData).toBeTruthy();
    
    if (consentData) {
      const parsedConsent = JSON.parse(consentData);
      expect(parsedConsent['external-videos']).toBe(true);
    }
    
    // Step 3: Close and reopen browser (new context)
    await context.close();
    const newContext = await page.context().browser()?.newContext();
    if (newContext) {
      const newPage = await newContext.newPage();
      
      // Simulate returning user with stored consent
      await newPage.evaluate((consentData) => {
        localStorage.setItem('rideatlas-consents', consentData);
      }, consentData);
      
      await newPage.goto('/cookie-settings');
      
      const newVideoToggle = newPage.locator('text=Video Esterni')
        .locator('..')
        .locator('input[type="checkbox"]');
        
      if (await newVideoToggle.isVisible()) {
        await expect(newVideoToggle).toBeChecked();
      }
      
      await newContext.close();
    }
  });
});