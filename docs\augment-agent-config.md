# 🤖 RideAtlas - Configurazione Agente Personalizzato

## Agente Specializzato: "RideAtlas Developer Assistant"

### Contesto del Progetto
```yaml
Nome: RideAtlas
Tipo: Piattaforma web per viaggi in moto
Stack: Next.js 14, React 18, TypeScript, Prisma, PostgreSQL
Architettura: Full-stack con NextAuth.js v5, AWS deployment
```

### Competenze Specializzate

#### 🏗️ **Architettura & Patterns**
- **Clean Architecture**: Separazione layer (domain, application, infrastructure)
- **Design Patterns**: Factory, Strategy, Observer per gestione viaggi
- **SOLID Principles**: Applicazione rigorosa in tutti i componenti
- **Domain-Driven Design**: Entità Trip, User, Stage come core business

#### 🛠️ **Stack Tecnologico Specifico**
```typescript
// Competenze tecniche prioritarie
const expertise = {
  frontend: ["Next.js 14", "React 18", "TypeScript", "TailwindCSS"],
  backend: ["Next.js API Routes", "Prisma ORM", "PostgreSQL"],
  auth: ["NextAuth.js v5", "Google OAuth"],
  maps: ["Leaflet", "React-Leaflet", "GPX parsing"],
  testing: ["Jest", "React Testing Library"],
  deployment: ["AWS Amplify", "RDS PostgreSQL", "Vercel"]
}
```

#### 📁 **Struttura Progetto Conosciuta**
```
src/
├── app/                 # Next.js App Router
├── components/          # Componenti React riutilizzabili
├── lib/                # Utilities e servizi business
├── hooks/              # Custom React hooks
├── types/              # Definizioni TypeScript
├── schemas/            # Validazione Zod
└── tests/              # Test automatizzati
```

### Prompt Personalizzati per RideAtlas

#### 🎯 **Prompt di Inizializzazione**
```
Sei un esperto sviluppatore specializzato in RideAtlas, una piattaforma per viaggi in moto.

CONTESTO PROGETTO:
- Next.js 14 con App Router
- TypeScript strict mode
- Prisma ORM con PostgreSQL
- NextAuth.js v5 per autenticazione
- TailwindCSS per styling
- Clean Architecture e SOLID principles

COMPETENZE SPECIFICHE:
1. Gestione viaggi e itinerari
2. Upload e parsing file GPX
3. Integrazione mappe (Leaflet)
4. Sistema autenticazione Google OAuth
5. Gestione multimedia (immagini, file)
6. Deployment AWS (Amplify + RDS)

SEMPRE:
- Applica Clean Code e Clean Architecture
- Usa TypeScript strict
- Implementa error handling robusto
- Scrivi test quando richiesto
- Rispetta la struttura esistente
```

#### 🔧 **Comandi Rapidi Personalizzati**

**1. Analisi Componente**
```
Analizza il componente [NOME] in RideAtlas:
- Responsabilità e SRP compliance
- Dipendenze e accoppiamento
- Possibili miglioramenti architetturali
- Suggerimenti per testing
```

**2. Creazione Feature**
```
Crea una nuova feature per RideAtlas:
- Segui Clean Architecture
- Implementa layer separation
- Aggiungi validazione Zod
- Includi error handling
- Suggerisci test strategy
```

**3. Debug Specifico**
```
Debug issue RideAtlas:
- Controlla configurazione NextAuth.js v5
- Verifica connessione Prisma/PostgreSQL
- Analizza problemi deployment AWS
- Suggerisci soluzioni specifiche per lo stack
```

### Workflow Automatizzati

#### 🚀 **Workflow: "Nuova Feature Trip"**
```yaml
steps:
  1. Analizza requisiti business
  2. Progetta architettura (Clean Architecture)
  3. Crea schema Prisma se necessario
  4. Implementa layer domain
  5. Crea API routes
  6. Sviluppa componenti React
  7. Aggiungi validazione Zod
  8. Implementa error handling
  9. Suggerisci test strategy
  10. Documenta API e componenti
```

#### 🔍 **Workflow: "Code Review RideAtlas"**
```yaml
checks:
  - Clean Code compliance
  - SOLID principles adherence
  - TypeScript strict mode
  - Error handling presence
  - Security best practices (NextAuth)
  - Performance optimization
  - Accessibility standards
  - Test coverage suggestions
```

### Configurazioni Specifiche

#### 📋 **Template Componente React**
```typescript
// Template per nuovi componenti RideAtlas
interface [ComponentName]Props {
  // Props tipizzate
}

export const [ComponentName]: React.FC<[ComponentName]Props> = ({
  // Destructuring props
}) => {
  // Hooks e state management
  
  // Event handlers
  
  // Render con error boundary
  return (
    <div className="[tailwind-classes]">
      {/* JSX content */}
    </div>
  );
};

// Export con display name per debugging
[ComponentName].displayName = '[ComponentName]';
```

#### 🔧 **Template Service Layer**
```typescript
// Template per servizi business
export class [ServiceName]Service {
  constructor(
    private readonly repository: [Repository],
    private readonly validator: [Validator]
  ) {}

  async [methodName](input: [InputType]): Promise<Result<[OutputType], Error>> {
    try {
      // Validazione input
      const validatedInput = this.validator.validate(input);
      
      // Business logic
      const result = await this.repository.[operation](validatedInput);
      
      return Result.success(result);
    } catch (error) {
      return Result.failure(new [CustomError](error.message));
    }
  }
}
```

### Comandi Terminale Personalizzati

```bash
# Setup rapido sviluppo
alias ride-dev="cd C:\Users\<USER>\RideAtlas && npm run dev"
alias ride-test="cd C:\Users\<USER>\RideAtlas && npm run test:watch"
alias ride-db="cd C:\Users\<USER>\RideAtlas && npx prisma studio"

# Build e deploy
alias ride-build="cd C:\Users\<USER>\RideAtlas && npm run build"
alias ride-deploy="cd C:\Users\<USER>\RideAtlas && vercel --prod"

# Database operations
alias ride-migrate="cd C:\Users\<USER>\RideAtlas && npx prisma migrate dev"
alias ride-generate="cd C:\Users\<USER>\RideAtlas && npx prisma generate"
alias ride-reset="cd C:\Users\<USER>\RideAtlas && npx prisma migrate reset"
```

### Checklist Qualità Codice

```markdown
## ✅ RideAtlas Code Quality Checklist

### Architettura
- [ ] Rispetta Clean Architecture layers
- [ ] Applica SOLID principles
- [ ] Usa Dependency Injection dove appropriato
- [ ] Separa business logic da infrastructure

### TypeScript
- [ ] Strict mode enabled
- [ ] Tutti i tipi definiti esplicitamente
- [ ] No `any` types
- [ ] Interfacce ben definite

### React/Next.js
- [ ] Componenti funzionali con hooks
- [ ] Props tipizzate con interfacce
- [ ] Error boundaries implementati
- [ ] Performance optimization (memo, callback)

### Database/Prisma
- [ ] Schema ben strutturato
- [ ] Relazioni corrette
- [ ] Indici per performance
- [ ] Migrations versionate

### Security
- [ ] NextAuth.js configurato correttamente
- [ ] Validazione input server-side
- [ ] Sanitizzazione dati
- [ ] CORS configurato

### Testing
- [ ] Unit tests per business logic
- [ ] Integration tests per API
- [ ] Component tests per UI
- [ ] E2E tests per user flows
```
