# GDPR Implementation for YouTube Videos - Option B

## Overview
Implementazione del controllo granulare del consenso per video YouTube nella galleria multimediale, estendendo il sistema di cookie consent esistente.

## Current Status
- ✅ CookieBanner base implementato in `src/components/ui/CookieBanner.tsx`
- ✅ Sistema di consenso generico "Accetta tutti" / "Rifiuta non essenziali"
- ❌ Manca controllo granulare per categorie specifiche (video esterni)

## Required Implementation

### 1. Extend Cookie Consent Categories

**File da modificare:** `src/components/ui/CookieBanner.tsx`

**Modifiche necessarie:**
- Aggiungere categoria "external-videos" al sistema di consenso
- Implementare UI per controllo granulare delle categorie
- Gestire stato separato per diversi tipi di cookie

**Categorie proposte:**
```typescript
enum CookieCategory {
  ESSENTIAL = 'essential',      // Sempre attivi
  FUNCTIONAL = 'functional',    // Funzionalità sito
  EXTERNAL_VIDEOS = 'external-videos', // Video YouTube
  ANALYTICS = 'analytics'       // Google Analytics (futuro)
}
```

### 2. Cookie Consent Service

**Nuovo file:** `src/lib/cookie-consent.ts`

**Funzionalità richieste:**
```typescript
interface CookieConsentService {
  // Controlla se una categoria è consentita
  hasConsent(category: CookieCategory): boolean;
  
  // Imposta consenso per categoria
  setConsent(category: CookieCategory, granted: boolean): void;
  
  // Ottieni tutti i consensi
  getAllConsents(): Record<CookieCategory, boolean>;
  
  // Reset tutti i consensi
  resetConsents(): void;
  
  // Subscribe a cambiamenti consenso
  onConsentChange(callback: (consents: Record<CookieCategory, boolean>) => void): void;
}
```

### 3. Video Consent Dialog

**Nuovo componente:** `src/components/ui/VideoConsentDialog.tsx`

**Props richieste:**
```typescript
interface VideoConsentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: () => void;
  videoTitle?: string;
}
```

**Funzionalità:**
- Dialog modale per consenso specifico video
- Spiegazione chiara su cosa traccia YouTube
- Opzioni: "Accetta per questo video" / "Accetta per tutti i video" / "Rifiuta"
- Link alla privacy policy YouTube

### 4. UnifiedMediaGallery Integration

**File:** `src/components/ui/UnifiedMediaGallery.tsx`

**Logica di controllo consenso:**
```typescript
const handleVideoPlay = (videoId: string) => {
  if (cookieConsentService.hasConsent(CookieCategory.EXTERNAL_VIDEOS)) {
    // Carica iframe direttamente
    loadYouTubeVideo(videoId);
  } else {
    // Mostra dialog consenso
    setShowVideoConsentDialog(true);
    setPendingVideoId(videoId);
  }
};

const onVideoConsentAccept = (rememberChoice: boolean) => {
  if (rememberChoice) {
    cookieConsentService.setConsent(CookieCategory.EXTERNAL_VIDEOS, true);
  }
  loadYouTubeVideo(pendingVideoId);
  setShowVideoConsentDialog(false);
};
```

### 5. Consent Settings Page (Optional)

**Nuovo file:** `src/app/cookie-settings/page.tsx`

**Funzionalità:**
- Pagina dedicata per gestire preferenze cookie
- Toggle per ogni categoria
- Spiegazioni dettagliate per categoria
- Save/Reset preferences
- Link dalla privacy policy

### 6. Privacy Policy Updates

**File da aggiornare:** `src/app/privacy-policy/page.tsx`

**Sezioni da aggiungere:**
- Informazioni specifiche su video YouTube embedded
- Dati raccolti da YouTube quando si riproducono video
- Come controllare/revocare consenso per video
- Link alle policy YouTube/Google

## Implementation Priority

### Phase 1 (Immediate)
1. Creare `CookieConsentService`
2. Creare `VideoConsentDialog`
3. Integrare controllo consenso in `UnifiedMediaGallery`

### Phase 2 (Next Sprint)
1. Estendere `CookieBanner` per controllo granulare
2. Aggiornare Privacy Policy
3. Creare pagina Cookie Settings

### Phase 3 (Future)
1. Analytics integration con stesso sistema
2. Altri servizi esterni (mappe, social media)

## Technical Notes

### Cookie Storage
- Utilizzare `localStorage` per persistenza consensi
- Formato: `rideatlas-consents: {external-videos: true, analytics: false, ...}`
- Scadenza: 365 giorni (allineata a banner attuale)

### YouTube URLs
- Preferire `youtube-nocookie.com` quando possibile
- Usare `youtube.com` standard per ora (come richiesto)
- Parametri iframe: `?rel=0&modestbranding=1` per ridurre tracking

### Compliance
- Documentare tutte le chiamate esterne
- Log delle decisioni di consenso (solo statistiche, no dati personali)
- Fornire facile opt-out

## Testing Requirements

- [ ] Consent dialog appare al primo tentativo di riproduzione video
- [ ] Preferenze vengono salvate e rispettate
- [ ] Video caricati solo dopo consenso
- [ ] Reset preferenze funziona correttamente
- [ ] Privacy policy aggiornata e completa

## Future Considerations

- **GDPR Audit**: Revisione legale completa
- **Cookie Scanner**: Tool automatico per rilevare cookie
- **Consent Mode v2**: Google Analytics enhanced ecommerce
- **User Dashboard**: Gestione completa dati personali